import datetime
import random

def get_greeting():
    """根据时间返回不同的问候语"""
    hour = datetime.datetime.now().hour
    if 5 <= hour < 12:
        return "早上好"
    elif 12 <= hour < 18:
        return "下午好"
    else:
        return "晚上好"

def get_random_emoji():
    """返回随机表情符号"""
    emojis = ["😊", "🎉", "✨", "🚀", "💻", "🌟", "🎯", "🔥"]
    return random.choice(emojis)

def get_random_quote():
    """返回随机励志名言"""
    quotes = [
        "行动是治愈恐惧的良药。 —— 威廉·詹姆斯",
        "伟大的事业不是靠力气，速度和身体的敏捷完成的，而是靠性格，意志和知识的力量完成的。 —— 塞缪尔·约翰逊",
        "成功不是将来才有的，而是从决定去做的那一刻起，持续累积而成。 —— 佚名",
        "每一个不曾起舞的日子，都是对生命的辜负。 —— 尼采",
        "不要等待机会，而要创造机会。 —— 林肯"
    ]
    return random.choice(quotes)

def get_weather_forecast():
    """模拟获取天气预报"""
    weather_types = ["晴朗", "多云", "小雨", "大雨", "雷雨", "小雪", "大雪", "雾霾"]
    temperatures = range(0, 35)
    
    weather = random.choice(weather_types)
    temp = random.choice(temperatures)
    
    return f"{weather}，温度 {temp}°C"

def play_guessing_game():
    """简单的数字猜测游戏"""
    number_to_guess = random.randint(1, 100)
    attempts = 0
    
    print("\n🎮 欢迎来到数字猜测游戏！")
    print("我已经想好了一个1到100之间的数字，你能猜到它吗？")
    
    while True:
        try:
            guess = int(input("请输入你的猜测: "))
            attempts += 1
            
            if guess < number_to_guess:
                print("太小了！再试一次。")
            elif guess > number_to_guess:
                print("太大了！再试一次。")
            else:
                print(f"🎉 恭喜你！你猜对了！答案是 {number_to_guess}")
                print(f"你总共猜了 {attempts} 次。")
                break
        except ValueError:
            print("请输入一个有效的数字！")
def print_banner():
    """打印装饰性横幅"""
    print("=" * 50)
    print("🎊 欢迎使用 Augment Agent 演示程序 🎊")
    print("=" * 50)

def main():
    print_banner()

    # 获取当前时间
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    greeting = get_greeting()
    emoji = get_random_emoji()

    print(f"\n{greeting}！{emoji}")
    print(f"当前时间：{current_time}")
    print(f"今日天气：{get_weather_forecast()}")
    print("\nHello World - 现在由 Augment Agent 增强！")

    # 简单的用户交互
    try:
        name = input("\n请输入你的名字：")
        if name.strip():
            print(f"\n很高兴认识你，{name}！{get_random_emoji()}")
            print(f"Augment Agent 正在为你服务！")
            
            # 显示随机名言
            print(f"\n这是为你准备的一句话：\n「{get_random_quote()}」")
            
            # 添加简单的交互菜单
            while True:
                print("\n你想做什么？")
                print("1. 查看当前时间")
                print("2. 获取天气预报")
                print("3. 显示励志名言")
                print("4. 玩数字猜测游戏")
                print("5. 退出")
                print("4. 退出")
                
                choice = input("请选择 (1-5): ")
                
                if choice == "1":
                    print(f"当前时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                elif choice == "2":
                    print(f"天气预报：{get_weather_forecast()}")
                elif choice == "3":
                    print(f"励志名言：{get_random_quote()}")
                elif choice == "4":
                    play_guessing_game()
                elif choice == "5":
                    break
                elif choice == "4":
                    break
                else:
                    print("无效选择，请重试")
        else:
            print(f"\n匿名用户，你好！{get_random_emoji()}")
    except KeyboardInterrupt:
        print(f"\n\n再见！{get_random_emoji()}")

    print("\n" + "=" * 50)
    print("感谢使用！Augment Agent 运行正常 ✅")
    print("=" * 50)

if __name__ == "__main__":
    main()