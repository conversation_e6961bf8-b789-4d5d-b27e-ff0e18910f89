import json
def main(input_data) -> dict:
    isDev = True

    if isinstance(input_data, str):
        try:
            # 处理转义字符，注意处理顺序很重要
            processed_input = input_data

            # 先处理双反斜杠，避免影响其他转义字符的处理
            processed_input = processed_input.replace('\\\\', '\\')
            # 处理转义的引号
            processed_input = processed_input.replace('\\"', '"')
            # 处理转义的斜杠
            processed_input = processed_input.replace('\\/', '/')
            # 处理制表符
            processed_input = processed_input.replace('\\t', '\t')
            # 注意：不处理 \\n 和 \\r，让JSON解析器自己处理这些转义序列

            input_data = json.loads(processed_input)
            dataType = input_data.get('dataType', '无')
            name = input_data.get('name', '无')
            content = input_data.get('content', '无')
            otherInfo = input_data.get('otherInfo', '无')
            keywordsPool = input_data.get('keywordsPool', '无')
        except Exception:
            # 如果JSON解析失败，将整个字符串作为content返回
            dataType = ''
            name = ''
            content = input_data
            otherInfo = ''
            keywordsPool = ''
    else:
        dataType = input_data.get('dataType', '无')
        name = input_data.get('name', '无')
        content = input_data.get('content', '无')
        otherInfo = input_data.get('otherInfo', '无')
        keywordsPool = input_data.get('keywordsPool', '无')
    return {
        'dataType': dataType,
        'name': name,
        'content': content,
        'otherInfo': otherInfo,
        'keywordsPool': keywordsPool
    }

# 测试函数
if __name__ == "__main__":
    # 你的测试数据
    test_input = {
        "input_data": "{\n\t\"name\": \"莱芜钢铁集团电子有限公司应急调度平台项目\",\n\t\"content\": \"应急调度平台建设\",\n\t\"dataType\": \"项目\",\n\t\"otherInfo\": \"项目线：IT类-系统集成服务,主产品：网络集成,辅助产品1：网络安全,辅助产品2：无\",\n\t\"keywordsPool\": \" 一呼百应,数字乡村,智慧社区,智慧工地,智慧园区,\\r\\n智慧校园,智慧医院,智慧交通,智慧农业,智慧文旅,视频监控,智能安防, 人脸识别, 门禁系统, 周界报警,5G专网,5G定制网, 综合布线, 网络建设, 机房建设,\\r\\n光纤网络,无线网络, 专线接入,电子政务,政务云, 电子签章, OA系统, ERP系统,数据中心,云平台,大数据平台,等保测评,网络安全, 运维服务,技术支持,系统集成\"\n}"
    }

    # 测试处理
    result = main(test_input["input_data"])
    print("处理结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
